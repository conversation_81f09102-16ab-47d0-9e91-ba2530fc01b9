{"name": "AI文字处理聊天工作流", "nodes": [{"parameters": {"httpMethod": "POST", "path": "ai-chat", "responseMode": "responseNode", "options": {}}, "id": "webhook-node", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "your-webhook-id"}, {"parameters": {"jsCode": "// 提取用户消息\nconst userMessage = $input.first().json.message;\nconst sessionId = $input.first().json.sessionId;\nconst timestamp = $input.first().json.timestamp;\n\n// 这里可以添加你的文字处理逻辑\n// 例如：调用OpenAI API、Claude API或其他AI服务\n\n// 示例：简单的文字处理\nlet processedText = userMessage;\n\n// 添加一些示例处理逻辑\nif (userMessage.includes('优化')) {\n  processedText = `已为您优化文字内容：\\n\\n${userMessage.replace('优化', '').trim()}\\n\\n优化建议：\\n1. 语言更加简洁明了\\n2. 结构更加清晰\\n3. 表达更加专业`;\n} else if (userMessage.includes('翻译')) {\n  processedText = `翻译功能正在处理中...\\n\\n原文：${userMessage}\\n\\n注：请在此处集成实际的翻译API`;\n} else if (userMessage.includes('总结')) {\n  processedText = `文字总结：\\n\\n${userMessage}\\n\\n关键要点：\\n• 主要内容概括\\n• 重要信息提取\\n• 核心观点整理`;\n} else {\n  processedText = `AI助手收到您的消息：\"${userMessage}\"\\n\\n我可以帮您：\\n• 优化文字内容\\n• 翻译文字\\n• 总结长文本\\n• 其他文字处理任务\\n\\n请告诉我您需要什么帮助！`;\n}\n\nreturn {\n  message: userMessage,\n  response: processedText,\n  sessionId: sessionId,\n  timestamp: timestamp,\n  processedAt: new Date().toISOString()\n};"}, "id": "code-node", "name": "文字处理逻辑", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"response\": $json.response,\n  \"sessionId\": $json.sessionId,\n  \"timestamp\": $json.processedAt\n} }}"}, "id": "respond-node", "name": "返回响应", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 300]}], "connections": {"Webhook": {"main": [[{"node": "文字处理逻辑", "type": "main", "index": 0}]]}, "文字处理逻辑": {"main": [[{"node": "返回响应", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "Asia/Shanghai"}, "versionId": "1"}