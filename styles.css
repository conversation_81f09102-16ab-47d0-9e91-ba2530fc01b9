* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    overflow: hidden;
}

.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 800px;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chat-header h1 {
    font-size: 24px;
    margin-bottom: 5px;
}

.chat-header p {
    opacity: 0.9;
    font-size: 14px;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

.message {
    display: flex;
    margin-bottom: 20px;
    animation: fadeIn 0.3s ease-in;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    background: #28a745;
    color: white;
    margin-right: 0;
    margin-left: 12px;
}

.message-content {
    background: white;
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    max-width: 70%;
    position: relative;
}

.user-message .message-content {
    background: #007bff;
    color: white;
}

.message-content p {
    margin: 0;
    line-height: 1.4;
    word-wrap: break-word;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    display: block;
    margin-top: 4px;
}

.chat-input-container {
    padding: 20px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 8px;
    border: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.input-wrapper:focus-within {
    border-color: #667eea;
}

#messageInput {
    flex: 1;
    border: none;
    background: transparent;
    padding: 8px 12px;
    font-size: 14px;
    resize: none;
    outline: none;
    max-height: 120px;
    min-height: 20px;
}

.send-button {
    width: 40px;
    height: 40px;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease;
}

.send-button:hover {
    transform: scale(1.05);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-size: 12px;
    color: #6c757d;
}

.status {
    display: flex;
    align-items: center;
    gap: 5px;
}

.status .fa-circle {
    font-size: 8px;
    color: #28a745;
}

.status.disconnected .fa-circle {
    color: #dc3545;
}

.char-count {
    font-size: 11px;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.loading-spinner i {
    font-size: 24px;
    color: #667eea;
    margin-bottom: 10px;
}

.settings-panel {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 100;
}

.settings-toggle {
    width: 50px;
    height: 50px;
    border: none;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: #667eea;
    transition: all 0.3s ease;
}

.settings-toggle:hover {
    background: white;
    transform: rotate(90deg);
}

.settings-content {
    position: absolute;
    top: 60px;
    right: 0;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    width: 300px;
    border: 1px solid #e9ecef;
}

.settings-content h3 {
    margin-bottom: 15px;
    color: #333;
}

.setting-group {
    margin-bottom: 15px;
}

.setting-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.setting-group input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

.save-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    width: 100%;
    font-weight: 500;
}

.save-button:hover {
    opacity: 0.9;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chat-container {
        height: 100vh;
        border-radius: 0;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .settings-content {
        width: 280px;
        right: -10px;
    }
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
