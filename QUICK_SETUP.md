# 快速设置指南

## 🚀 你的n8n配置信息

**Webhook URL:** `https://n8n-esatlzhy.ap-southeast-1.clawcloudrun.com/webhook-test/1568f3ad-6133-4a91-a895-dc365d8b33c5`

## ⚡ 快速开始步骤

### 1. 设置n8n工作流

1. **登录你的n8n实例**：
   - 访问：`https://n8n-esatlzhy.ap-southeast-1.clawcloudrun.com`
   - 使用你的登录凭据

2. **导入工作流**：
   - 点击 "Import from file" 或 "+" 按钮
   - 选择 `your-n8n-workflow-markdown.json` 文件（支持丰富的Markdown格式）
   - 或者选择 `your-n8n-workflow.json` 文件（基础版本）
   - 或者复制文件内容直接粘贴

3. **激活工作流**：
   - 确保工作流中的Webhook节点路径为：`1568f3ad-6133-4a91-a895-dc365d8b33c5`
   - 点击右上角的 "Active" 开关激活工作流
   - 工作流激活后，webhook就可以接收请求了

### 2. 测试聊天应用

1. **打开聊天界面**：
   - 在浏览器中访问：`http://localhost:8000`
   - 界面已经预配置了你的webhook地址

2. **开始对话**：
   - 直接在输入框中输入消息
   - 尝试以下测试消息：
     - "你好，请介绍一下你的功能"
     - "请帮我优化这段文字：今天天气很好"
     - "翻译成英文：你好世界"
     - "总结一下：人工智能是未来科技发展的重要方向"
     - "格式化这个文档"
     - "检查语法错误"
     - "显示数学公式"
     - "分析代码"

## 🎯 功能测试清单

### ✅ 基础功能测试
- [ ] 发送简单消息
- [ ] 接收AI回复
- [ ] 查看连接状态（右下角应显示"已配置"）

### ✅ 文字处理功能测试
- [ ] **文字优化**：发送包含"优化"关键词的消息
- [ ] **内容翻译**：发送包含"翻译"关键词的消息
- [ ] **文本总结**：发送包含"总结"关键词的消息
- [ ] **格式调整**：发送包含"格式"关键词的消息
- [ ] **文字校对**：发送包含"检查"关键词的消息
- [ ] **代码处理**：发送包含"代码"关键词的消息
- [ ] **数学公式**：发送包含"数学"或"公式"关键词的消息

### ✅ Markdown渲染测试
- [ ] **标题渲染**：查看AI回复中的标题层级
- [ ] **列表显示**：检查有序和无序列表
- [ ] **表格渲染**：查看表格格式是否正确
- [ ] **代码高亮**：检查代码块语法高亮
- [ ] **数学公式**：验证LaTeX公式渲染
- [ ] **引用块**：查看引用内容样式
- [ ] **链接和强调**：测试粗体、斜体、链接

### ✅ 界面功能测试
- [ ] 消息发送动画
- [ ] 加载状态显示
- [ ] 错误处理提示
- [ ] 移动端适配

## 🔧 如果遇到问题

### 问题1：发送消息后没有回复
**可能原因：**
- n8n工作流未激活
- webhook路径不匹配

**解决方案：**
1. 检查n8n工作流是否已激活（Active开关为绿色）
2. 确认webhook节点的路径设置正确
3. 查看n8n执行日志是否有错误

### 问题2：显示"未配置"状态
**解决方案：**
1. 点击右上角设置按钮
2. 确认webhook URL正确填写
3. 点击"保存设置"

### 问题3：CORS错误
**解决方案：**
1. 在n8n工作流的Webhook节点中添加CORS设置
2. 或在n8n环境变量中设置：`N8N_CORS_ORIGIN=*`

## 🎨 Markdown渲染功能

聊天应用现在支持丰富的Markdown格式渲染：

### 支持的Markdown功能
- ✅ **标题**：# ## ### 等多级标题
- ✅ **文本格式**：**粗体**、*斜体*、`代码`
- ✅ **列表**：有序列表、无序列表、任务列表
- ✅ **表格**：完整的表格支持，包含对齐
- ✅ **代码块**：语法高亮，支持多种编程语言
- ✅ **数学公式**：LaTeX格式，支持内联和块级公式
- ✅ **引用块**：> 引用内容
- ✅ **分隔线**：--- 水平分隔线
- ✅ **链接**：[文字](URL) 格式链接

### 测试Markdown渲染
访问 `http://localhost:8000/markdown-test.html` 可以测试Markdown渲染功能。

## 📱 移动端使用

聊天应用完全支持移动设备：
- 响应式设计自动适配屏幕大小
- 触摸友好的界面元素
- 移动端优化的输入体验
- Markdown内容在移动端同样完美显示

## 🎨 自定义配置

### 修改默认webhook地址
如果需要更改webhook地址，编辑 `script.js` 文件第11行：
```javascript
this.webhookUrl = localStorage.getItem('webhookUrl') || '你的新webhook地址';
```

### 自定义样式
修改 `styles.css` 文件来调整：
- 颜色主题
- 字体大小
- 布局间距
- 动画效果

## 🚀 进阶功能

### 集成真实AI服务
要连接OpenAI、Claude或其他AI服务：
1. 使用 `n8n-workflow-openai-example.json` 作为模板
2. 在n8n中配置相应的API凭据
3. 修改工作流逻辑调用AI API

### 添加用户认证
在n8n工作流中添加身份验证逻辑：
```javascript
// 在Code节点中添加
const apiKey = $input.first().json.apiKey;
if (!apiKey || apiKey !== 'your-secret-key') {
  throw new Error('无效的API密钥');
}
```

### 消息历史记录
可以在n8n中添加数据库节点来存储对话历史：
- 使用MySQL、PostgreSQL或MongoDB节点
- 存储sessionId、消息内容和时间戳
- 实现对话上下文功能

## 📞 技术支持

如果遇到技术问题：
1. 查看浏览器控制台错误信息
2. 检查n8n工作流执行日志
3. 参考 `README.md` 中的详细文档
4. 查看 `DEPLOYMENT.md` 了解部署相关问题

## 🎉 恭喜！

如果所有测试都通过，你的AI文字处理聊天系统就已经成功搭建完成了！

现在你可以：
- 与AI助手进行自然对话
- 处理各种文字优化需求
- 享受流畅的聊天体验
- 根据需要扩展更多功能

祝你使用愉快！🎊
