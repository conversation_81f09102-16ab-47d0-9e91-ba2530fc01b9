{"name": "AI文字处理聊天工作流 - 你的配置", "nodes": [{"parameters": {"httpMethod": "POST", "path": "1568f3ad-6133-4a91-a895-dc365d8b33c5", "responseMode": "responseNode", "options": {"allowedOrigins": "*"}}, "id": "webhook-node", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "1568f3ad-6133-4a91-a895-dc365d8b33c5"}, {"parameters": {"jsCode": "// 提取用户消息和会话信息\nconst userMessage = $input.first().json.message || '';\nconst sessionId = $input.first().json.sessionId || 'default';\nconst timestamp = $input.first().json.timestamp || new Date().toISOString();\n\n// 验证输入\nif (!userMessage.trim()) {\n  return {\n    response: '请输入有效的消息内容。',\n    sessionId: sessionId,\n    timestamp: new Date().toISOString(),\n    error: true\n  };\n}\n\n// AI文字处理逻辑\nlet processedText = '';\nconst message = userMessage.toLowerCase();\n\n// 根据关键词提供不同的处理服务\nif (message.includes('优化') || message.includes('改进') || message.includes('润色')) {\n  processedText = `📝 **文字优化结果**\\n\\n原文：${userMessage}\\n\\n✨ **优化建议：**\\n\\n1. **语言表达**：使用更加简洁明了的表述\\n2. **结构调整**：重新组织段落逻辑，提升可读性\\n3. **专业性**：增强表达的专业度和准确性\\n4. **流畅度**：改善语言流畅性，避免重复表达\\n\\n💡 **优化后的文字：**\\n[这里应该是经过AI处理后的优化文本]\\n\\n如需进一步优化，请提供更具体的要求。`;\n\n} else if (message.includes('翻译')) {\n  const targetLang = message.includes('英文') || message.includes('english') ? '英文' : \n                    message.includes('中文') || message.includes('chinese') ? '中文' : '目标语言';\n  \n  processedText = `🌐 **翻译服务**\\n\\n原文：${userMessage}\\n\\n🎯 **翻译为：** ${targetLang}\\n\\n📋 **翻译结果：**\\n[这里应该是翻译后的文本]\\n\\n✅ **翻译质量检查：**\\n- 准确性：保持原意不变\\n- 流畅性：符合目标语言习惯\\n- 专业性：使用恰当的专业术语\\n\\n如需调整翻译风格或指定特定领域，请告知。`;\n\n} else if (message.includes('总结') || message.includes('摘要')) {\n  processedText = `📊 **文本总结**\\n\\n原文：${userMessage}\\n\\n🔍 **核心要点：**\\n\\n• **主要内容**：[提取的主要信息]\\n• **关键观点**：[重要观点总结]\\n• **核心数据**：[重要数据或事实]\\n\\n📝 **简要总结：**\\n[这里是精炼的总结内容]\\n\\n🎯 **结论：**\\n[总结性结论]\\n\\n需要更详细的分析或特定角度的总结吗？`;\n\n} else if (message.includes('格式') || message.includes('排版')) {\n  processedText = `📄 **格式优化**\\n\\n原文：${userMessage}\\n\\n🎨 **格式化建议：**\\n\\n1. **标题层级**：使用清晰的标题结构\\n2. **段落分割**：合理分段，提升可读性\\n3. **列表使用**：重要信息用列表形式展示\\n4. **重点标记**：使用粗体、斜体突出重点\\n\\n✨ **格式化后的文本：**\\n\\n# 主标题\\n\\n## 副标题\\n\\n**重点内容**\\n\\n- 要点一\\n- 要点二\\n- 要点三\\n\\n*补充说明*\\n\\n需要特定的格式要求吗？`;\n\n} else if (message.includes('检查') || message.includes('校对')) {\n  processedText = `🔍 **文字校对**\\n\\n原文：${userMessage}\\n\\n📋 **检查结果：**\\n\\n✅ **语法检查**：[语法问题及修正建议]\\n✅ **拼写检查**：[拼写错误及正确写法]\\n✅ **标点符号**：[标点使用建议]\\n✅ **用词准确性**：[用词建议]\\n\\n🛠️ **修正建议：**\\n[具体的修改建议]\\n\\n📝 **校对后文本：**\\n[校对后的完整文本]\\n\\n还需要检查其他方面吗？`;\n\n} else {\n  processedText = `🤖 **AI文字处理助手**\\n\\n您好！我收到了您的消息：\"${userMessage}\"\\n\\n💼 **我可以为您提供以下服务：**\\n\\n📝 **文字优化** - 改善表达、提升专业性\\n🌐 **内容翻译** - 多语言翻译服务\\n📊 **文本总结** - 提取要点、生成摘要\\n📄 **格式调整** - 优化排版、改善结构\\n🔍 **文字校对** - 语法检查、拼写纠正\\n\\n🎯 **使用方法：**\\n请在消息中包含关键词，如：\\n- \"请帮我优化这段文字...\"\\n- \"翻译成英文...\"\\n- \"总结一下...\"\\n- \"检查语法...\"\\n\\n请告诉我您需要什么帮助！`;\n}\n\n// 返回处理结果\nreturn {\n  message: userMessage,\n  response: processedText,\n  sessionId: sessionId,\n  timestamp: new Date().toISOString(),\n  processingTime: Date.now() - new Date(timestamp).getTime(),\n  wordCount: userMessage.length\n};"}, "id": "text-processor", "name": "文字处理器", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"response\": $json.response,\n  \"sessionId\": $json.sessionId,\n  \"timestamp\": $json.timestamp,\n  \"metadata\": {\n    \"processingTime\": $json.processingTime + \"ms\",\n    \"wordCount\": $json.wordCount,\n    \"service\": \"AI文字处理助手\"\n  }\n} }}"}, "id": "respond-node", "name": "返回响应", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 300]}], "connections": {"Webhook": {"main": [[{"node": "文字处理器", "type": "main", "index": 0}]]}, "文字处理器": {"main": [[{"node": "返回响应", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "Asia/Shanghai", "executionOrder": "v1"}, "staticData": null, "tags": ["AI", "文字处理", "聊天"], "triggerCount": 0, "updatedAt": "2024-06-28T09:00:00.000Z", "versionId": "1"}