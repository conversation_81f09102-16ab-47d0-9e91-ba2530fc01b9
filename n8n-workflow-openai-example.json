{"name": "AI文字处理聊天工作流 - OpenAI集成", "nodes": [{"parameters": {"httpMethod": "POST", "path": "ai-chat-openai", "responseMode": "responseNode", "options": {}}, "id": "webhook-node", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "// 提取和验证输入数据\nconst userMessage = $input.first().json.message;\nconst sessionId = $input.first().json.sessionId || 'default';\nconst timestamp = $input.first().json.timestamp || new Date().toISOString();\n\n// 验证消息不为空\nif (!userMessage || userMessage.trim() === '') {\n  throw new Error('消息内容不能为空');\n}\n\n// 构建OpenAI API请求\nconst systemPrompt = `你是一个专业的文字处理助手。你的任务是帮助用户优化、翻译、总结和处理各种文字内容。\n\n请根据用户的需求提供以下服务：\n1. 文字优化：改善语言表达、结构和流畅度\n2. 内容翻译：准确翻译各种语言\n3. 文本总结：提取关键信息和要点\n4. 格式调整：改善文档结构和排版\n5. 语言润色：提升文字的专业性和可读性\n\n请用中文回复，保持专业、友好的语调。`;\n\nreturn {\n  userMessage: userMessage,\n  sessionId: sessionId,\n  timestamp: timestamp,\n  systemPrompt: systemPrompt\n};"}, "id": "prepare-data", "name": "准备数据", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"resource": "chat", "operation": "create", "model": "gpt-3.5-turbo", "messages": {"values": [{"role": "system", "content": "={{ $json.systemPrompt }}"}, {"role": "user", "content": "={{ $json.userMessage }}"}]}, "options": {"temperature": 0.7, "maxTokens": 1000}}, "id": "openai-node", "name": "OpenAI Chat", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [680, 300], "credentials": {"openAiApi": {"id": "your-openai-credential-id", "name": "OpenAI API"}}}, {"parameters": {"jsCode": "// 处理OpenAI响应\nconst openaiResponse = $input.first().json;\nconst userData = $('prepare-data').first().json;\n\n// 提取AI回复内容\nlet aiResponse = '';\nif (openaiResponse.choices && openaiResponse.choices.length > 0) {\n  aiResponse = openaiResponse.choices[0].message.content;\n} else {\n  aiResponse = '抱歉，AI处理出现问题，请稍后重试。';\n}\n\n// 构建最终响应\nconst finalResponse = {\n  response: aiResponse,\n  sessionId: userData.sessionId,\n  timestamp: new Date().toISOString(),\n  originalMessage: userData.userMessage,\n  model: 'gpt-3.5-turbo',\n  tokensUsed: openaiResponse.usage ? openaiResponse.usage.total_tokens : 0\n};\n\nreturn finalResponse;"}, "id": "format-response", "name": "格式化响应", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"response\": $json.response,\n  \"sessionId\": $json.sessionId,\n  \"timestamp\": $json.timestamp,\n  \"metadata\": {\n    \"model\": $json.model,\n    \"tokensUsed\": $json.tokensUsed\n  }\n} }}"}, "id": "respond-node", "name": "返回响应", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"jsCode": "// 错误处理逻辑\nconst error = $input.first().json;\nconst errorMessage = error.message || '处理请求时发生未知错误';\n\n// 记录错误（可以发送到日志系统）\nconsole.error('AI聊天工作流错误:', errorMessage);\n\n// 返回用户友好的错误信息\nreturn {\n  response: `抱歉，处理您的请求时遇到了问题：${errorMessage}\\n\\n请检查：\\n1. 网络连接是否正常\\n2. API配置是否正确\\n3. 稍后重试\\n\\n如果问题持续存在，请联系技术支持。`,\n  sessionId: 'error-session',\n  timestamp: new Date().toISOString(),\n  error: true\n};"}, "id": "error-handler", "name": "错误处理", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 500]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"response\": $json.response,\n  \"sessionId\": $json.sessionId,\n  \"timestamp\": $json.timestamp,\n  \"error\": true\n} }}"}, "id": "error-response", "name": "错误响应", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 500]}], "connections": {"Webhook": {"main": [[{"node": "准备数据", "type": "main", "index": 0}]]}, "准备数据": {"main": [[{"node": "OpenAI Chat", "type": "main", "index": 0}]]}, "OpenAI Chat": {"main": [[{"node": "格式化响应", "type": "main", "index": 0}]]}, "格式化响应": {"main": [[{"node": "返回响应", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"timezone": "Asia/Shanghai", "errorWorkflow": {"errorWorkflow": "error-handler"}}, "staticData": null, "tags": ["AI", "聊天", "文字处理"], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}