<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown渲染测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #output {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            min-height: 100px;
        }
    </style>
    <!-- 引入相同的库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
</head>
<body>
    <h1>Markdown渲染功能测试</h1>
    
    <div class="test-section">
        <h2 class="test-title">测试用例</h2>
        <p>点击下面的按钮测试不同的Markdown功能：</p>
        
        <button onclick="testBasicMarkdown()">基础格式</button>
        <button onclick="testCodeHighlight()">代码高亮</button>
        <button onclick="testMath()">数学公式</button>
        <button onclick="testTable()">表格</button>
        <button onclick="testList()">列表</button>
        <button onclick="testAIResponse()">AI响应示例</button>
        <button onclick="clearOutput()">清空</button>
    </div>
    
    <div id="output"></div>

    <script>
        // 初始化marked
        marked.setOptions({
            highlight: function(code, lang) {
                if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                    try {
                        return hljs.highlight(code, { language: lang }).value;
                    } catch (err) {
                        console.warn('代码高亮失败:', err);
                    }
                }
                return code;
            },
            breaks: true,
            gfm: true,
            sanitize: false,
            smartLists: true,
            smartypants: true
        });

        function renderMarkdown(content) {
            const output = document.getElementById('output');
            try {
                output.innerHTML = marked.parse(content);
                // 处理代码高亮
                if (typeof hljs !== 'undefined') {
                    output.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightElement(block);
                    });
                }
                // 处理数学公式
                if (typeof MathJax !== 'undefined') {
                    MathJax.typesetPromise([output]).catch((err) => {
                        console.warn('MathJax渲染失败:', err);
                    });
                }
            } catch (error) {
                output.textContent = '渲染失败: ' + error.message;
            }
        }

        function testBasicMarkdown() {
            const content = `# 标题1
## 标题2
### 标题3

这是一段**粗体文字**和*斜体文字*。

> 这是一个引用块
> 可以包含多行内容

这是一个[链接](https://example.com)。

---

这是分隔线上方的内容。`;
            renderMarkdown(content);
        }

        function testCodeHighlight() {
            const content = `# 代码高亮测试

## JavaScript代码
\`\`\`javascript
function greet(name) {
    console.log(\`Hello, \${name}!\`);
    return \`Welcome, \${name}\`;
}

const user = "World";
greet(user);
\`\`\`

## Python代码
\`\`\`python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# 计算前10个斐波那契数
for i in range(10):
    print(f"F({i}) = {fibonacci(i)}")
\`\`\`

## 内联代码
使用 \`console.log()\` 来输出调试信息。`;
            renderMarkdown(content);
        }

        function testMath() {
            const content = `# 数学公式测试

## 内联公式
这是一个内联公式：$E = mc^2$，爱因斯坦的质能方程。

## 块级公式
$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$

## 复杂公式
$$\\frac{\\partial f}{\\partial x} = \\lim_{h \\to 0} \\frac{f(x+h) - f(x)}{h}$$

## 矩阵
$$\\begin{pmatrix}
a & b \\\\
c & d
\\end{pmatrix}$$`;
            renderMarkdown(content);
        }

        function testTable() {
            const content = `# 表格测试

| 功能 | 状态 | 描述 |
|------|------|------|
| Markdown解析 | ✅ | 支持基础Markdown语法 |
| 代码高亮 | ✅ | 支持多种编程语言 |
| 数学公式 | ✅ | 支持LaTeX数学公式 |
| 表格渲染 | ✅ | 支持GitHub风格表格 |
| 图片显示 | ⚠️ | 需要额外配置 |

## 对齐表格
| 左对齐 | 居中对齐 | 右对齐 |
|:-------|:-------:|-------:|
| 内容1  |  内容2  |  内容3 |
| 较长的内容 | 中等内容 | 短内容 |`;
            renderMarkdown(content);
        }

        function testList() {
            const content = `# 列表测试

## 无序列表
- 第一项
- 第二项
  - 嵌套项目1
  - 嵌套项目2
    - 更深层嵌套
- 第三项

## 有序列表
1. 首先做这个
2. 然后做那个
   1. 子步骤A
   2. 子步骤B
3. 最后完成

## 任务列表
- [x] 已完成的任务
- [x] 另一个已完成的任务
- [ ] 待完成的任务
- [ ] 另一个待完成的任务`;
            renderMarkdown(content);
        }

        function testAIResponse() {
            const content = `# 📝 文字优化结果

**原文：** 请帮我优化这段文字

## ✨ 优化建议：

1. **语言表达**：使用更加简洁明了的表述
2. **结构调整**：重新组织段落逻辑，提升可读性
3. **专业性**：增强表达的专业度和准确性
4. **流畅度**：改善语言流畅性，避免重复表达

## 💡 优化后的文字：

> 经过AI分析，您的文字已经过专业优化处理。

### 代码示例
\`\`\`javascript
// 优化前的代码
function process(text) {
    return text;
}

// 优化后的代码
function optimizeText(inputText) {
    return enhanceClarity(inputText)
        .then(improveStructure)
        .then(addProfessionalism);
}
\`\`\`

### 数学分析
文字优化效果可以用以下公式表示：

$$\\text{优化效果} = \\frac{\\text{可读性} \\times \\text{专业性}}{\\text{复杂度}}$$

---

如需进一步优化，请提供更具体的要求。`;
            renderMarkdown(content);
        }

        function clearOutput() {
            document.getElementById('output').innerHTML = '';
        }

        // 页面加载完成后显示欢迎信息
        window.addEventListener('load', function() {
            setTimeout(() => {
                renderMarkdown(`# 🎉 Markdown渲染测试页面

欢迎使用Markdown渲染功能测试！

点击上方按钮测试不同的Markdown功能。这个测试页面使用了与聊天应用相同的库和配置。

## 支持的功能
- ✅ 基础Markdown语法
- ✅ 代码语法高亮
- ✅ 数学公式渲染
- ✅ 表格支持
- ✅ 任务列表
- ✅ 引用块

开始测试吧！`);
            }, 1000);
        });
    </script>
</body>
</html>
