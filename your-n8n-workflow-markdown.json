{
  "name": "AI文字处理聊天工作流 - Markdown优化版",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "1568f3ad-6133-4a91-a895-dc365d8b33c5",
        "responseMode": "responseNode",
        "options": {
          "allowedOrigins": "*"
        }
      },
      "id": "webhook-node",
      "name": "Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300],
      "webhookId": "1568f3ad-6133-4a91-a895-dc365d8b33c5"
    },
    {
      "parameters": {
        "jsCode": "// 提取用户消息和会话信息\nconst userMessage = $input.first().json.message || '';\nconst sessionId = $input.first().json.sessionId || 'default';\nconst timestamp = $input.first().json.timestamp || new Date().toISOString();\n\n// 验证输入\nif (!userMessage.trim()) {\n  return {\n    response: '请输入有效的消息内容。',\n    sessionId: sessionId,\n    timestamp: new Date().toISOString(),\n    error: true\n  };\n}\n\n// AI文字处理逻辑\nlet processedText = '';\nconst message = userMessage.toLowerCase();\n\n// 根据关键词提供不同的处理服务\nif (message.includes('优化') || message.includes('改进') || message.includes('润色')) {\n  processedText = `# 📝 文字优化结果\n\n**原文：** ${userMessage}\n\n## ✨ 优化建议：\n\n1. **语言表达**：使用更加简洁明了的表述\n2. **结构调整**：重新组织段落逻辑，提升可读性  \n3. **专业性**：增强表达的专业度和准确性\n4. **流畅度**：改善语言流畅性，避免重复表达\n\n## 💡 优化后的文字：\n\n> 经过AI分析处理的优化版本将在这里显示。\n> \n> *注：这是示例响应，实际使用时请集成真实的AI服务。*\n\n### 📊 优化效果分析\n\n| 维度 | 优化前 | 优化后 | 提升度 |\n|------|--------|--------|--------|\n| 可读性 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | +67% |\n| 专业性 | ⭐⭐ | ⭐⭐⭐⭐ | +100% |\n| 简洁性 | ⭐⭐ | ⭐⭐⭐⭐⭐ | +150% |\n\n---\n\n💬 如需进一步优化，请提供更具体的要求。`;\n\n} else if (message.includes('翻译')) {\n  const targetLang = message.includes('英文') || message.includes('english') ? '英文' : \n                    message.includes('中文') || message.includes('chinese') ? '中文' : '目标语言';\n  \n  processedText = `# 🌐 翻译服务\n\n**原文：** ${userMessage}\n\n**目标语言：** ${targetLang}\n\n## 📋 翻译结果：\n\n\`\`\`\n[这里应该是翻译后的文本]\n\`\`\`\n\n## ✅ 翻译质量检查：\n\n- **准确性**：保持原意不变\n- **流畅性**：符合目标语言习惯\n- **专业性**：使用恰当的专业术语\n- **文化适应性**：考虑文化差异\n\n### 🔍 详细分析\n\n| 评估项 | 得分 | 说明 |\n|--------|------|------|\n| 语法正确性 | ⭐⭐⭐⭐⭐ | 语法结构完全正确 |\n| 语义准确性 | ⭐⭐⭐⭐ | 意思传达准确 |\n| 表达自然性 | ⭐⭐⭐⭐ | 符合目标语言习惯 |\n\n---\n\n如需调整翻译风格或指定特定领域，请告知。`;\n\n} else if (message.includes('总结') || message.includes('摘要')) {\n  processedText = `# 📊 文本总结\n\n**原文：** ${userMessage}\n\n## 🔍 核心要点：\n\n### 📌 主要内容\n- 提取的主要信息点1\n- 提取的主要信息点2\n- 提取的主要信息点3\n\n### 💡 关键观点\n1. **重要观点1**：详细说明\n2. **重要观点2**：详细说明\n3. **重要观点3**：详细说明\n\n### 📈 核心数据\n> 重要数据或事实的引用展示\n\n## 📝 简要总结：\n\n**一句话总结：** [精炼的核心内容]\n\n**详细总结：** [更完整的总结内容，包含主要论点和支撑信息]\n\n## 🎯 结论：\n\n\`\`\`\n最终的总结性结论\n\`\`\`\n\n---\n\n需要更详细的分析或特定角度的总结吗？`;\n\n} else if (message.includes('格式') || message.includes('排版')) {\n  processedText = `# 📄 格式优化\n\n**原文：** ${userMessage}\n\n## 🎨 格式化建议：\n\n### 1. 标题层级\n使用清晰的标题结构，建立信息层次\n\n### 2. 段落分割\n合理分段，提升可读性和理解效率\n\n### 3. 列表使用\n重要信息用列表形式展示，便于快速浏览\n\n### 4. 重点标记\n使用**粗体**、*斜体*突出重点内容\n\n## ✨ 格式化后的文本：\n\n\`\`\`markdown\n# 主标题\n\n## 副标题\n\n**重点内容**\n\n- 要点一\n- 要点二\n- 要点三\n\n*补充说明*\n\n> 引用或重要提示\n\n### 代码示例\n\\`\\`\\`\n代码内容\n\\`\\`\\`\n\`\`\`\n\n### 📋 格式化检查清单\n\n- [x] 标题层级清晰\n- [x] 段落结构合理\n- [x] 重点内容突出\n- [x] 列表格式规范\n- [ ] 需要进一步调整\n\n---\n\n需要特定的格式要求吗？`;\n\n} else if (message.includes('检查') || message.includes('校对')) {\n  processedText = `# 🔍 文字校对\n\n**原文：** ${userMessage}\n\n## 📋 检查结果：\n\n### ✅ 语法检查\n- **发现问题：** [语法问题描述]\n- **修正建议：** [具体修正方案]\n\n### ✅ 拼写检查\n- **错误词汇：** \\`错误拼写\\` → \\`正确拼写\\`\n- **建议：** [拼写改进建议]\n\n### ✅ 标点符号\n- **标点问题：** [标点使用问题]\n- **规范建议：** [标点规范建议]\n\n### ✅ 用词准确性\n- **用词建议：** [更准确的用词选择]\n\n## 🛠️ 修正建议：\n\n\`\`\`diff\n- 原始文本中的问题部分\n+ 修正后的文本内容\n\`\`\`\n\n## 📝 校对后文本：\n\n> 经过校对和修正的完整文本内容\n\n### 📊 校对统计\n\n| 检查项目 | 发现问题 | 已修正 | 建议关注 |\n|----------|----------|--------|-----------|\n| 语法错误 | 2 | 2 | 0 |\n| 拼写错误 | 1 | 1 | 0 |\n| 标点问题 | 3 | 3 | 0 |\n| 用词优化 | 5 | 5 | 2 |\n\n---\n\n还需要检查其他方面吗？`;\n\n} else if (message.includes('代码') || message.includes('编程')) {\n  processedText = `# 💻 代码处理\n\n**您的请求：** ${userMessage}\n\n## 🔧 代码分析\n\n### JavaScript 示例\n\n\\`\\`\\`javascript\n// 示例代码\nfunction processText(input) {\n    // 文字处理逻辑\n    const processed = input\n        .trim()\n        .toLowerCase()\n        .replace(/\\\\s+/g, ' ');\n    \n    return {\n        original: input,\n        processed: processed,\n        length: processed.length\n    };\n}\n\n// 使用示例\nconst result = processText(\"${userMessage}\");\nconsole.log(result);\n\\`\\`\\`\n\n### Python 示例\n\n\\`\\`\\`python\ndef process_text(input_text):\n    \"\"\"文字处理函数\"\"\"\n    processed = input_text.strip().lower()\n    \n    return {\n        'original': input_text,\n        'processed': processed,\n        'length': len(processed)\n    }\n\n# 使用示例\nresult = process_text(\"${userMessage}\")\nprint(result)\n\\`\\`\\`\n\n## 📊 代码质量评估\n\n| 指标 | 评分 | 说明 |\n|------|------|------|\n| 可读性 | ⭐⭐⭐⭐ | 代码结构清晰 |\n| 效率 | ⭐⭐⭐⭐⭐ | 执行效率高 |\n| 维护性 | ⭐⭐⭐⭐ | 易于维护 |\n\n---\n\n需要其他编程语言的示例吗？`;\n\n} else if (message.includes('数学') || message.includes('公式')) {\n  processedText = `# 🧮 数学公式处理\n\n**您的请求：** ${userMessage}\n\n## 📐 数学表达式\n\n### 基础公式\n内联公式：$E = mc^2$\n\n### 复杂公式\n$$\\\\int_{-\\\\infty}^{\\\\infty} e^{-x^2} dx = \\\\sqrt{\\\\pi}$$\n\n### 矩阵表示\n$$\\\\begin{pmatrix}\na & b \\\\\\\\\nc & d\n\\\\end{pmatrix}$$\n\n### 求和公式\n$$\\\\sum_{i=1}^{n} i = \\\\frac{n(n+1)}{2}$$\n\n## 📊 数学分析\n\n| 公式类型 | 复杂度 | 应用领域 |\n|----------|--------|----------|\n| 线性代数 | ⭐⭐⭐ | 工程计算 |\n| 微积分 | ⭐⭐⭐⭐ | 物理建模 |\n| 统计学 | ⭐⭐⭐⭐⭐ | 数据分析 |\n\n---\n\n需要特定的数学公式帮助吗？`;\n\n} else {\n  processedText = `# 🤖 AI文字处理助手\n\n您好！我收到了您的消息：\n\n> ${userMessage}\n\n## 💼 我可以为您提供以下服务：\n\n### 📝 文字优化\n- 改善表达方式\n- 提升专业性\n- 增强可读性\n\n### 🌐 内容翻译\n- 多语言翻译\n- 专业术语处理\n- 文化适应性调整\n\n### 📊 文本总结\n- 提取关键要点\n- 生成精炼摘要\n- 结构化分析\n\n### 📄 格式调整\n- 优化排版布局\n- 改善文档结构\n- Markdown格式化\n\n### 🔍 文字校对\n- 语法检查\n- 拼写纠正\n- 标点规范\n\n### 💻 代码处理\n- 代码分析\n- 语法高亮\n- 最佳实践建议\n\n### 🧮 数学公式\n- LaTeX公式渲染\n- 数学表达式\n- 科学计算\n\n## 🎯 使用方法：\n\n请在消息中包含相关关键词：\n\n\\`\\`\\`\n\"请帮我优化这段文字...\"\n\"翻译成英文...\"\n\"总结一下...\"\n\"检查语法...\"\n\"格式化这个文档...\"\n\"分析这段代码...\"\n\"显示数学公式...\"\n\\`\\`\\`\n\n---\n\n🚀 **开始使用吧！告诉我您需要什么帮助。**`;\n}\n\n// 返回处理结果\nreturn {\n  message: userMessage,\n  response: processedText,\n  sessionId: sessionId,\n  timestamp: new Date().toISOString(),\n  processingTime: Date.now() - new Date(timestamp).getTime(),\n  wordCount: userMessage.length,\n  hasMarkdown: true\n};"
      },
      "id": "text-processor",
      "name": "文字处理器",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [460, 300]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ {\n  \"response\": $json.response,\n  \"sessionId\": $json.sessionId,\n  \"timestamp\": $json.timestamp,\n  \"metadata\": {\n    \"processingTime\": $json.processingTime + \"ms\",\n    \"wordCount\": $json.wordCount,\n    \"hasMarkdown\": $json.hasMarkdown,\n    \"service\": \"AI文字处理助手 - Markdown版\"\n  }\n} }}"
      },
      "id": "respond-node",
      "name": "返回响应",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [680, 300]
    }
  ],
  "connections": {
    "Webhook": {
      "main": [
        [
          {
            "node": "文字处理器",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "文字处理器": {
      "main": [
        [
          {
            "node": "返回响应",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": true,
  "settings": {
    "timezone": "Asia/Shanghai",
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": ["AI", "文字处理", "聊天", "Markdown"],
  "triggerCount": 0,
  "updatedAt": "2024-06-28T09:00:00.000Z",
  "versionId": "2"
}
