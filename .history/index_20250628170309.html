<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI文字处理助手</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1><i class="fas fa-robot"></i> AI文字处理助手</h1>
            <p>基于n8n工作流的智能对话系统</p>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message bot-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <p>你好！我是AI文字处理助手。请告诉我你需要处理什么文字内容，我会帮你进行优化和处理。</p>
                    <span class="message-time">刚刚</span>
                </div>
            </div>
        </div>
        
        <div class="chat-input-container">
            <div class="input-wrapper">
                <textarea 
                    id="messageInput" 
                    placeholder="输入你的消息..." 
                    rows="1"
                    maxlength="2000"
                ></textarea>
                <button id="sendButton" class="send-button">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            <div class="input-footer">
                <span class="char-count">0/2000</span>
                <span class="status" id="connectionStatus">
                    <i class="fas fa-circle"></i> 已连接
                </span>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>AI正在处理中...</p>
        </div>
    </div>

    <!-- 设置面板 -->
    <div class="settings-panel" id="settingsPanel">
        <button class="settings-toggle" id="settingsToggle">
            <i class="fas fa-cog"></i>
        </button>
        <div class="settings-content" style="display: none;">
            <h3>连接设置</h3>
            <div class="setting-group">
                <label for="webhookUrl">n8n Webhook URL:</label>
                <input type="url" id="webhookUrl" placeholder="https://your-n8n-instance.com/webhook/your-webhook-id">
            </div>
            <div class="setting-group">
                <label for="apiKey">API密钥 (可选):</label>
                <input type="password" id="apiKey" placeholder="输入API密钥">
            </div>
            <button id="saveSettings" class="save-button">保存设置</button>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
