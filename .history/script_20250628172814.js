class ChatApp {
    constructor() {
        this.chatMessages = document.getElementById('chatMessages');
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.loadingOverlay = document.getElementById('loadingOverlay');
        this.connectionStatus = document.getElementById('connectionStatus');
        this.settingsToggle = document.getElementById('settingsToggle');
        this.settingsPanel = document.getElementById('settingsPanel');
        this.webhookUrlInput = document.getElementById('webhookUrl');
        this.apiKeyInput = document.getElementById('apiKey');
        this.saveSettingsButton = document.getElementById('saveSettings');

        this.webhookUrl = localStorage.getItem('webhookUrl') || 'https://n8n-esatlzhy.ap-southeast-1.clawcloudrun.com/webhook-test/1568f3ad-6133-4a91-a895-dc365d8b33c5';
        this.apiKey = localStorage.getItem('apiKey') || '';

        this.initializeMarkdown();
        this.initializeEventListeners();
        this.loadSettings();
        this.updateConnectionStatus();
    }

    initializeMarkdown() {
        // 配置marked.js
        if (typeof marked !== 'undefined') {
            marked.setOptions({
                highlight: function(code, lang) {
                    if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                        try {
                            return hljs.highlight(code, { language: lang }).value;
                        } catch (err) {
                            console.warn('代码高亮失败:', err);
                        }
                    }
                    return code;
                },
                breaks: true,
                gfm: true,
                sanitize: false,
                smartLists: true,
                smartypants: true
            });
        }
    }

    initializeEventListeners() {
        // 发送消息
        this.sendButton.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 自动调整输入框高度
        this.messageInput.addEventListener('input', () => {
            this.autoResizeTextarea();
            this.updateCharCount();
        });

        // 设置面板
        this.settingsToggle.addEventListener('click', () => this.toggleSettings());
        this.saveSettingsButton.addEventListener('click', () => this.saveSettings());

        // 点击外部关闭设置面板
        document.addEventListener('click', (e) => {
            if (!this.settingsPanel.contains(e.target)) {
                this.hideSettings();
            }
        });
    }

    autoResizeTextarea() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }

    updateCharCount() {
        const charCount = this.messageInput.value.length;
        const charCountElement = document.querySelector('.char-count');
        charCountElement.textContent = `${charCount}/2000`;
        
        if (charCount > 1800) {
            charCountElement.style.color = '#dc3545';
        } else if (charCount > 1500) {
            charCountElement.style.color = '#ffc107';
        } else {
            charCountElement.style.color = '#6c757d';
        }
    }

    toggleSettings() {
        const settingsContent = this.settingsPanel.querySelector('.settings-content');
        settingsContent.classList.toggle('hidden');
    }

    hideSettings() {
        const settingsContent = this.settingsPanel.querySelector('.settings-content');
        settingsContent.classList.add('hidden');
    }

    loadSettings() {
        this.webhookUrlInput.value = this.webhookUrl;
        this.apiKeyInput.value = this.apiKey;
    }

    saveSettings() {
        this.webhookUrl = this.webhookUrlInput.value.trim();
        this.apiKey = this.apiKeyInput.value.trim();
        
        localStorage.setItem('webhookUrl', this.webhookUrl);
        localStorage.setItem('apiKey', this.apiKey);
        
        this.updateConnectionStatus();
        this.hideSettings();
        
        this.showNotification('设置已保存', 'success');
    }

    updateConnectionStatus() {
        const statusElement = this.connectionStatus;
        const circle = statusElement.querySelector('.fa-circle');
        
        if (this.webhookUrl) {
            statusElement.innerHTML = '<i class="fas fa-circle"></i> 已配置';
            circle.style.color = '#28a745';
        } else {
            statusElement.innerHTML = '<i class="fas fa-circle"></i> 未配置';
            circle.style.color = '#dc3545';
        }
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message) return;

        if (!this.webhookUrl) {
            this.showNotification('请先配置n8n Webhook URL', 'error');
            this.toggleSettings();
            return;
        }

        // 添加用户消息到聊天
        this.addMessage(message, 'user');
        this.messageInput.value = '';
        this.autoResizeTextarea();
        this.updateCharCount();

        // 禁用发送按钮
        this.sendButton.disabled = true;
        this.showLoading(true);

        try {
            const response = await this.callN8nWebhook(message);
            this.addMessage(response, 'bot');
        } catch (error) {
            console.error('发送消息失败:', error);
            this.addMessage('抱歉，处理您的请求时出现了错误。请检查网络连接和配置。', 'bot', true);
        } finally {
            this.sendButton.disabled = false;
            this.showLoading(false);
        }
    }

    async callN8nWebhook(message) {
        const headers = {
            'Content-Type': 'application/json',
        };

        // 如果有API密钥，添加到请求头
        if (this.apiKey) {
            headers['Authorization'] = `Bearer ${this.apiKey}`;
        }

        const requestBody = {
            message: message,
            timestamp: new Date().toISOString(),
            sessionId: this.getSessionId()
        };

        const response = await fetch(this.webhookUrl, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        
        // n8n可能返回不同的响应格式，这里处理常见的情况
        if (data.response) {
            return data.response;
        } else if (data.message) {
            return data.message;
        } else if (data.result) {
            return data.result;
        } else if (typeof data === 'string') {
            return data;
        } else {
            return JSON.stringify(data, null, 2);
        }
    }

    getSessionId() {
        let sessionId = localStorage.getItem('sessionId');
        if (!sessionId) {
            sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('sessionId', sessionId);
        }
        return sessionId;
    }

    addMessage(content, type, isError = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;

        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'message-avatar';
        avatarDiv.innerHTML = type === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        if (isError) {
            contentDiv.style.background = '#f8d7da';
            contentDiv.style.color = '#721c24';
            contentDiv.style.border = '1px solid #f5c6cb';
        }

        // 创建消息内容容器
        const messageContentDiv = document.createElement('div');
        messageContentDiv.className = 'markdown-content';

        // 渲染Markdown内容
        if (typeof marked !== 'undefined' && type === 'bot') {
            try {
                messageContentDiv.innerHTML = marked.parse(content);
                // 处理代码高亮
                if (typeof hljs !== 'undefined') {
                    messageContentDiv.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightElement(block);
                    });
                }
            } catch (error) {
                console.warn('Markdown渲染失败，使用纯文本:', error);
                messageContentDiv.textContent = content;
            }
        } else {
            // 用户消息或Markdown不可用时使用纯文本
            messageContentDiv.textContent = content;
        }

        const timeSpan = document.createElement('span');
        timeSpan.className = 'message-time';
        timeSpan.textContent = this.formatTime(new Date());

        contentDiv.appendChild(messageContentDiv);
        contentDiv.appendChild(timeSpan);
        messageDiv.appendChild(avatarDiv);
        messageDiv.appendChild(contentDiv);

        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();

        // 如果包含数学公式，重新渲染MathJax
        if (typeof MathJax !== 'undefined' && type === 'bot') {
            MathJax.typesetPromise([messageContentDiv]).catch((err) => {
                console.warn('MathJax渲染失败:', err);
            });
        }
    }

    formatTime(date) {
        return date.toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    }

    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    showLoading(show) {
        if (show) {
            this.loadingOverlay.classList.remove('hidden');
        } else {
            this.loadingOverlay.classList.add('hidden');
        }
    }

    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 5px;
            color: white;
            font-weight: 500;
            z-index: 1001;
            animation: slideIn 0.3s ease;
        `;
        
        switch (type) {
            case 'success':
                notification.style.background = '#28a745';
                break;
            case 'error':
                notification.style.background = '#dc3545';
                break;
            default:
                notification.style.background = '#17a2b8';
        }
        
        notification.textContent = message;
        document.body.appendChild(notification);
        
        // 3秒后自动移除
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// 添加滑入动画的CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ChatApp();
});
