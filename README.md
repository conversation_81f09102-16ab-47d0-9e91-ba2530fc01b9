# AI文字处理助手 - ChatGPT风格聊天界面

这是一个基于Web的聊天界面，用于与你的n8n AI文字处理工作流进行对话交互。

## 功能特性

- 🤖 **ChatGPT风格界面** - 现代化的聊天界面设计
- 🔗 **n8n集成** - 直接与你的n8n工作流通信
- 📱 **响应式设计** - 支持桌面和移动设备
- ⚙️ **灵活配置** - 可配置webhook URL和API密钥
- 💬 **实时对话** - 流畅的消息发送和接收体验
- 🔒 **会话管理** - 自动生成和维护会话ID
- 📊 **状态指示** - 连接状态和加载状态显示

## 快速开始

### 1. 设置n8n工作流

在你的n8n工作流中，确保有一个Webhook节点作为入口点：

1. 添加一个 **Webhook** 节点
2. 设置HTTP方法为 **POST**
3. 记录生成的Webhook URL
4. 确保工作流能够处理以下格式的请求：

```json
{
  "message": "用户输入的消息",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "sessionId": "session_1234567890_abcdef"
}
```

### 2. 配置聊天界面

1. 打开 `index.html` 文件
2. 点击右上角的设置按钮（齿轮图标）
3. 输入你的n8n Webhook URL
4. （可选）输入API密钥用于身份验证
5. 点击"保存设置"

### 3. 开始对话

- 在输入框中输入你的消息
- 按Enter键或点击发送按钮
- 等待AI处理并返回结果

## n8n工作流响应格式

你的n8n工作流应该返回以下格式之一的JSON响应：

```json
{
  "response": "AI处理后的文字内容"
}
```

或者：

```json
{
  "message": "AI处理后的文字内容"
}
```

或者：

```json
{
  "result": "AI处理后的文字内容"
}
```

## 文件结构

```
├── index.html          # 主HTML文件
├── styles.css          # 样式文件
├── script.js           # JavaScript逻辑
└── README.md           # 说明文档
```

## 本地运行

由于浏览器的CORS限制，建议使用本地服务器运行：

### 方法1: 使用Python
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

### 方法2: 使用Node.js
```bash
npx serve .
```

### 方法3: 使用PHP
```bash
php -S localhost:8000
```

然后在浏览器中访问 `http://localhost:8000`

## 高级配置

### 自定义样式

你可以修改 `styles.css` 文件来自定义界面外观：

- 修改颜色主题
- 调整布局和间距
- 添加自定义动画

### 扩展功能

在 `script.js` 中可以添加更多功能：

- 消息历史记录
- 文件上传支持
- 多语言支持
- 语音输入

### n8n工作流示例

一个简单的n8n工作流可能包含：

1. **Webhook** 节点 - 接收聊天消息
2. **HTTP Request** 节点 - 调用AI API（如OpenAI、Claude等）
3. **Code** 节点 - 处理和格式化响应
4. **Respond to Webhook** 节点 - 返回处理结果

## 故障排除

### 常见问题

1. **无法发送消息**
   - 检查Webhook URL是否正确
   - 确认n8n工作流已激活
   - 检查网络连接

2. **CORS错误**
   - 使用本地服务器运行应用
   - 在n8n中配置CORS设置

3. **响应格式错误**
   - 确保n8n工作流返回正确的JSON格式
   - 检查响应字段名称

### 调试技巧

- 打开浏览器开发者工具查看网络请求
- 检查控制台错误信息
- 在n8n中查看工作流执行日志

## 安全注意事项

- 不要在客户端代码中硬编码敏感信息
- 使用HTTPS连接保护数据传输
- 在n8n中实施适当的身份验证
- 定期更新API密钥

## 许可证

MIT License - 可自由使用和修改
