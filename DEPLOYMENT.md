# 部署指南

本文档介绍如何将AI文字处理聊天应用部署到生产环境。

## 部署选项

### 1. 静态网站托管

由于这是一个纯前端应用，可以部署到任何静态网站托管服务：

#### Vercel (推荐)
```bash
# 安装Vercel CLI
npm i -g vercel

# 在项目目录中运行
vercel

# 按照提示完成部署
```

#### Netlify
1. 将代码推送到GitHub仓库
2. 在Netlify中连接GitHub仓库
3. 设置构建命令为空（因为是静态文件）
4. 设置发布目录为根目录

#### GitHub Pages
1. 将代码推送到GitHub仓库
2. 在仓库设置中启用GitHub Pages
3. 选择源分支（通常是main或gh-pages）

### 2. 自托管服务器

#### 使用Nginx
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    root /path/to/your/chat-app;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 启用gzip压缩
    gzip on;
    gzip_types text/css application/javascript text/javascript;
    
    # 缓存静态资源
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### 使用Apache
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/your/chat-app
    
    <Directory /path/to/your/chat-app>
        AllowOverride All
        Require all granted
    </Directory>
    
    # 启用压缩
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
    </Location>
</VirtualHost>
```

## n8n配置

### 1. n8n云服务
如果使用n8n云服务：
1. 登录n8n.cloud
2. 导入工作流文件（`n8n-workflow-example.json`或`n8n-workflow-openai-example.json`）
3. 配置必要的凭据（如OpenAI API密钥）
4. 激活工作流
5. 复制Webhook URL

### 2. 自托管n8n
```bash
# 使用Docker运行n8n
docker run -it --rm \
  --name n8n \
  -p 5678:5678 \
  -v ~/.n8n:/home/<USER>/.n8n \
  n8nio/n8n

# 或使用docker-compose
version: '3.7'
services:
  n8n:
    image: n8nio/n8n
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=your-password
    volumes:
      - ~/.n8n:/home/<USER>/.n8n
```

## 环境变量配置

创建一个配置文件来管理不同环境的设置：

```javascript
// config.js
const config = {
  development: {
    n8nWebhookUrl: 'http://localhost:5678/webhook/test',
    apiKey: '',
    debug: true
  },
  production: {
    n8nWebhookUrl: 'https://your-n8n-instance.com/webhook/your-id',
    apiKey: process.env.API_KEY || '',
    debug: false
  }
};

const environment = process.env.NODE_ENV || 'development';
export default config[environment];
```

## HTTPS配置

### 使用Let's Encrypt (免费SSL证书)
```bash
# 安装certbot
sudo apt-get install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行：
0 12 * * * /usr/bin/certbot renew --quiet
```

## 性能优化

### 1. 启用压缩
在服务器配置中启用gzip压缩以减少传输大小。

### 2. 缓存策略
```nginx
# 静态资源缓存
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# HTML文件不缓存
location ~* \.html$ {
    expires -1;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
}
```

### 3. CDN集成
考虑使用CDN服务来加速静态资源加载：
- Cloudflare
- AWS CloudFront
- Azure CDN

## 安全配置

### 1. CORS设置
在n8n中配置CORS以允许你的域名访问：

```javascript
// n8n环境变量
N8N_CORS_ORIGIN=https://your-chat-app.com
```

### 2. API密钥管理
- 使用环境变量存储敏感信息
- 定期轮换API密钥
- 实施访问控制

### 3. 速率限制
在n8n工作流中添加速率限制逻辑：

```javascript
// 在Code节点中添加
const sessionId = $input.first().json.sessionId;
const now = Date.now();
const rateLimitKey = `rate_limit_${sessionId}`;

// 检查速率限制（示例：每分钟最多10次请求）
const lastRequest = $workflow.staticData[rateLimitKey] || 0;
if (now - lastRequest < 6000) { // 6秒间隔
  throw new Error('请求过于频繁，请稍后再试');
}

$workflow.staticData[rateLimitKey] = now;
```

## 监控和日志

### 1. 错误监控
集成错误监控服务：
- Sentry
- LogRocket
- Bugsnag

### 2. 分析统计
添加网站分析：
- Google Analytics
- Plausible
- Mixpanel

### 3. 性能监控
监控应用性能：
- Web Vitals
- Lighthouse CI
- New Relic

## 备份和恢复

### 1. n8n工作流备份
定期导出n8n工作流配置：
```bash
# 使用n8n CLI导出工作流
n8n export:workflow --all --output=./backups/
```

### 2. 配置备份
备份重要配置文件：
- 服务器配置
- SSL证书
- 环境变量

## 故障排除

### 常见部署问题

1. **CORS错误**
   - 检查n8n CORS配置
   - 确认域名设置正确

2. **Webhook无法访问**
   - 检查防火墙设置
   - 确认n8n服务正在运行
   - 验证URL格式

3. **SSL证书问题**
   - 检查证书有效期
   - 验证域名配置
   - 确认证书链完整

### 调试工具

1. **浏览器开发者工具**
   - Network标签查看请求
   - Console查看错误信息

2. **n8n执行日志**
   - 查看工作流执行历史
   - 检查错误详情

3. **服务器日志**
   - Nginx/Apache访问日志
   - 系统错误日志

## 更新和维护

### 1. 应用更新
- 定期更新依赖库
- 测试新功能
- 备份现有配置

### 2. n8n更新
```bash
# 更新n8n Docker镜像
docker pull n8nio/n8n:latest
docker-compose up -d
```

### 3. 安全更新
- 定期更新服务器系统
- 更新SSL证书
- 检查安全漏洞

通过遵循这个部署指南，你可以将AI文字处理聊天应用成功部署到生产环境，并确保其稳定、安全地运行。
